# StageWise Cloud Makefile

.PHONY: help setup dev backend frontend seed schema types clean docker test

# Default target
help:
	@echo "StageWise Cloud - Available commands:"
	@echo "  setup     - Setup development environment"
	@echo "  dev       - Start full stack development (local)"
	@echo "  dev-docker - Start full stack development (Docker)"
	@echo "  backend   - Start backend only"
	@echo "  frontend  - Start frontend only"
	@echo "  seed      - Seed database with test data"
	@echo "  admin     - Create admin user manually"
	@echo "  schema    - Generate Go models from schema"
	@echo "  types     - Generate TypeScript types"
	@echo "  docker    - Start with Docker Compose"
	@echo "  test      - Run all tests"
	@echo "  clean     - Clean build artifacts"

setup:
	@chmod +x scripts/setup-dev.sh
	@./scripts/setup-dev.sh

dev:
	@./scripts/dev-local.sh

dev-docker:
	@docker-compose up

backend:
	@echo "🚀 Starting backend server..."
	@if [ -f .env.local ]; then \
		echo "📄 Loading environment from .env.local"; \
		set -o allexport; source .env.local; set +o allexport; \
	elif [ -f .env ]; then \
		echo "📄 Loading environment from .env"; \
		set -o allexport; source .env; set +o allexport; \
	fi; \
	cd backend && go run cmd/server/main.go serve

frontend:
	@bun run dev

seed:
	@echo "🌱 Seeding database with test data..."
	@if [ -f .env.local ]; then \
		echo "📄 Loading environment from .env.local"; \
		set -o allexport; source .env.local; set +o allexport; \
	elif [ -f .env ]; then \
		echo "📄 Loading environment from .env"; \
		set -o allexport; source .env; set +o allexport; \
	fi; \
	cd backend && go run cmd/seed_pb/main.go

schema:
	@cd backend && go run cmd/schema_gen/main.go \
		-inputFile schema/collections.json \
		-outputFile internal/generated_pocketbase_models.go \
		-packageName internal

types:
	@bunx pocketbase-typegen --url http://127.0.0.1:8090 --out ./src/types/pocketbase-generated.ts

docker:
	@docker-compose up --build

test:
	@bun run test:run
	@cd backend && go test ./...

admin:
	@echo "👤 Creating admin user..."
	@if [ -f .env.local ]; then \
		echo "📄 Loading environment from .env.local"; \
		set -o allexport; source .env.local; set +o allexport; \
	elif [ -f .env ]; then \
		echo "📄 Loading environment from .env"; \
		set -o allexport; source .env; set +o allexport; \
	fi; \
	cd backend && go run cmd/server/main.go superuser upsert $${POCKETBASE_ADMIN_EMAIL:-<EMAIL>} $${POCKETBASE_ADMIN_PASSWORD:-admin1234}

clean:
	@rm -rf dist/
	@rm -rf coverage/
	@rm -rf backend/pb_data/
	@docker-compose down --volumes --remove-orphans