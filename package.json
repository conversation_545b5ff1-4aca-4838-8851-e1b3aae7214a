{"name": "stagewisecloud", "private": true, "version": "0.1.0", "type": "module", "packageManager": "bun@1.2.7", "scripts": {"dev": "vite", "lint": "eslint .", "preview": "vite preview", "build:dev": "tsc && vite build --minify false --mode development", "build": "tsc && vite build --mode production", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "backend:dev": "cd backend && go run cmd/server/main.go serve", "backend:schema": "cd backend && go run cmd/schema_gen/main.go -inputFile schema/collections.json -outputFile internal/generated_pocketbase_models.go -packageName internal", "backend:seed": "cd backend && go run cmd/seed_pb/main.go", "backend:migrate": "cd backend && go run cmd/migrations/main.go", "backend:create-admin": "cd backend && go run cmd/server/main.go create-superuser", "backend:init-schema": "cd backend && go run cmd/init-schema/main.go", "types:generate": "bunx pocketbase-typegen --url http://127.0.0.1:8090 --out ./src/types/pocketbase-generated.ts", "dev:local": "concurrently --names \"<PERSON><PERSON><PERSON><PERSON>,FRONTEND\" --prefix-colors \"blue,green\" --kill-others-on-fail false \"bun run backend:dev\" \"wait-on http://127.0.0.1:8090/api/health && bun run dev || echo 'Frontend started despite esbuild warnings'\"", "dev:setup": "bun run backend:create-admin && echo 'Admin user created! Starting development servers...' && bun run dev:local", "fullstack:dev": "docker-compose up", "fullstack:build": "docker-compose up --build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.508.0", "motion": "^12.12.1", "next-themes": "^0.4.6", "pocketbase": "^0.26.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "^6.22.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "zod": "^3.25.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.17", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "jsdom": "^26.1.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.9", "typescript": "^5.2.2", "vitest": "^3.1.4", "pocketbase-typegen": "^1.2.1", "concurrently": "^9.1.0", "wait-on": "^8.0.1"}}