import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ItemTypesCategoryOptions,
  type ItemTypesRecord,
} from "@/types/pocketbase"; // Adjusted import path
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
// import { Separator } from "@/components/ui/separator"; // Not used in the provided code
import { ImageUploader } from "@/components/ImageUploader";
import { toast } from "sonner";
import { Barcode, Package, DollarSign, TestTube, ImageIcon, FileText, Tag, X } from "lucide-react"; // Added ImageIcon, FileText, Tag, X
// import { GetLocations } from "../../bindings/stagewise/inventoryservice"; // For fetching locations
// TODO: Replace Wails binding for GetLocations with an alternative (e.g., API call) to re-enable location fetching and selection.

// Create a schema for form validation
const inventoryFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  barcode: z.string().optional(),
  brand: z.string().optional(),
  category: z.enum(
    Object.values(ItemTypesCategoryOptions) as [string, ...string[]]
  ),
  subcategory: z.string().optional(),
  color: z.string().optional(),
  description: z.string().optional(),
  dimensions: z.string().optional(),
  style: z.string().optional(),
  sku: z.string().optional(),
  purchase_unit_price: z.coerce.number().optional(),
  replacement_unit_value: z.coerce
    .number()
    .min(0, { message: "Value must be a positive number" }),
  total_quantity: z.coerce
    .number()
    .min(1, { message: "Quantity must be at least 1" }),
  locationId: z.coerce.number().min(1, { message: "Location is required" }), // Added locationId
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof inventoryFormSchema>;

// Interface for field errors
interface FieldErrors {
  [key: string]: string;
}

interface InventoryItemFormProps {
  initialData?: Partial<
    Omit<
      ItemTypesRecord,
      | "id"
      | "collectionId"
      | "collectionName"
      | "created"
      | "updated"
      | "expand"
    > & { locationId?: number }
  >; // Adjusted to allow locationId in initialData
  onSubmit: (data: FormValues & { images: string[] }) => void;
  isLoading?: boolean;
  fieldErrors?: FieldErrors; // Add fieldErrors prop
}

const InventoryItemForm: React.FC<InventoryItemFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  fieldErrors = {}, // Default to empty object
}) => {
  const [images, setImages] = useState<string[]>(initialData?.images || []);
  const [locations, setLocations] = useState<{ ID: number; Name: string }[]>(
    []
  );



  // Apply field errors from parent component to form
  useEffect(() => {
    if (Object.keys(fieldErrors).length > 0) {
      // Set server-side errors on the form fields
      Object.entries(fieldErrors).forEach(([field, error]) => {
        form.setError(field as any, {
          type: 'server',
          message: error
        });
      });
    }
  }, [fieldErrors]);

  // useEffect(() => {
  //   async function fetchLocations() {
  //     try {
  //       // const locs = await GetLocations(); // Original Wails call
  //       const locs = []; // TODO: Replace with actual location fetching
  //       setLocations(locs);
  //
  //       // If initialData already specifies a location, respect it.
  //       // The form is initialized with this value via defaultValues.
  //       if (initialData?.locationId) {
  //         return;
  //       }
  //
  //       // If no initialData.locationId, try to set a default if the form field isn't already populated.
  //       const currentLocationValue = form.getValues("locationId");
  //       if (!currentLocationValue) {
  //         const homeLocation = locs.find(
  //           (loc) => loc.Name.toLowerCase() === "home"
  //         );
  //
  //         if (homeLocation) {
  //           form.setValue("locationId", homeLocation.ID, {
  //             shouldValidate: true,
  //             shouldDirty: true,
  //           });
  //         } else if (locs.length > 0) {
  //           // Fallback to the first location if "Home" is not found
  //           form.setValue("locationId", locs[0].ID, {
  //             shouldValidate: true,
  //             shouldDirty: true,
  //           });
  //         }
  //       }
  //     } catch (error) {
  //       toast.error("Failed to fetch locations");
  //     }
  //   }
  //   fetchLocations();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [initialData?.locationId]); // form.getValues and form.setValue are stable

  const defaultValues: Partial<FormValues> = {
    name: initialData?.name || "",
    barcode: initialData?.barcode || "",
    brand: initialData?.brand || "",
    category: initialData?.category || ItemTypesCategoryOptions.Decor, // Default to Decor
    subcategory: initialData?.subcategory || "",
    color: initialData?.color || "",
    description: initialData?.description || "",
    dimensions: initialData?.dimensions || "",
    style: initialData?.style || "",
    sku: initialData?.sku || "",
    purchase_unit_price: initialData?.purchase_unit_price || undefined,
    replacement_unit_value: initialData?.replacement_unit_value || 0,
    total_quantity: initialData?.total_quantity || 1,
    locationId: initialData?.locationId || undefined, // Added locationId
    notes: initialData?.notes || "",
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(inventoryFormSchema),
    defaultValues,
    mode: "onChange", // Or "onBlur" for validation trigger
  });

  const handleSubmit = (values: FormValues) => {
    // Don't proceed if already loading
    if (isLoading) return;

    console.log("Form submitted with:", { ...values, images });
    onSubmit({
      ...values,
      images,
    });
    // Toasting success is now handled by the parent component (inventoryAdd.tsx)
    // toast.success("Item saved successfully");
  };

  const isDevelopment = process.env.NODE_ENV === 'development';

  const generateMockData = (): Partial<FormValues> => {
    const categories = Object.values(ItemTypesCategoryOptions);
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    const randomLocationId = locations.length > 0 ? locations[Math.floor(Math.random() * locations.length)].ID : undefined;

    return {
      name: `Dev Item ${Date.now().toString().slice(-5)}`,
      barcode: `BC-${Math.random().toString().slice(2, 12)}`,
      brand: `Brand ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
      category: randomCategory,
      subcategory: `Subcat ${Math.floor(Math.random() * 100)}`,
      color: ['Red', 'Blue', 'Green', 'Black', 'White', 'Yellow', 'Purple'][Math.floor(Math.random() * 7)],
      description: `This is a development test description for item generated on ${new Date().toLocaleDateString()}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`,
      dimensions: `${Math.floor(Math.random() * 50 + 1)}L x ${Math.floor(Math.random() * 50 + 1)}W x ${Math.floor(Math.random() * 50 + 1)}H cm`,
      style: ['Modern', 'Vintage', 'Rustic', 'Industrial', 'Minimalist'][Math.floor(Math.random() * 5)],
      sku: `SKU-${Math.random().toString().slice(2, 10)}`,
      purchase_unit_price: parseFloat((Math.random() * 200).toFixed(2)),
      replacement_unit_value: parseFloat((Math.random() * 300 + 1).toFixed(2)), // Min 1
      total_quantity: Math.floor(Math.random() * 50) + 1, // Min 1
      locationId: randomLocationId,
      notes: `Development notes: This item was auto-generated for testing purposes on ${new Date().toLocaleTimeString()}.\nFeatures to test: ...\nKnown issues: ...`,
    };
  };

  const handleFillForm = () => {
    if (!isDevelopment) return;
    const mockData = generateMockData();

    const definedMockData = Object.fromEntries(
      Object.entries(mockData).filter(([_, v]) => v !== undefined)
    ) as Partial<FormValues>; // Type assertion

    form.reset(definedMockData);

    // Manually trigger re-validation for all fields after reset if needed
    // form.trigger();

    // Check if locationId was set and if it's valid
    if (mockData.locationId === undefined && locations.length > 0) {
      toast.info("Mock data applied. Location was not set from mock data as none was available at generation, but form's useEffect might set one.");
    } else if (mockData.locationId === undefined && locations.length === 0) {
      toast.info("Mock data applied, but no locations are loaded. Please select a location manually.");
    }

    toast.success("Form filled with mock data!", { description: "Images were not populated." });
  };

  const handleFormSubmission = (e: React.FormEvent) => {
    // Explicitly handle the form submission
    e.preventDefault();
    form.handleSubmit(handleSubmit)(e);
  };

  return (
    <Form {...form}>
      <form onSubmit={handleFormSubmission} className="space-y-8">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Card className="col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>
                <div className="flex items-center">
                  <ImageIcon className="mr-2 h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-medium">Images</h3>
                </div>
              </CardTitle>
              <CardDescription>
                Upload images for the inventory item. (Max 10 images)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageUploader
                images={images}
                setImages={setImages}
                maxImages={10}
              />
            </CardContent>
          </Card>

          <Card className="col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>
                <div className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-medium">Basic Information</h3>
                </div>
              </CardTitle>
              <CardDescription>Provide fundamental details about the item like name, brand, and category.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="lg:col-span-2">
                      <FormLabel>Item Name*</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter item name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter brand name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category*</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(ItemTypesCategoryOptions).map(
                            (category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="subcategory"
                  render={({ field }) => (
                    <FormItem className="lg:col-span-2">
                      <FormLabel>Subcategory</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter subcategory" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="lg:col-span-2">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter a detailed description of the item"
                          {...field}
                          className="min-h-[80px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>
                <div className="flex items-center">
                  <Package className="mr-2 h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-medium">Inventory Details & Notes</h3>
                </div>
              </CardTitle>
              <CardDescription>Specify stock levels, location, pricing, and add internal notes.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="total_quantity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Quantity*</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={1}
                            placeholder="Enter quantity"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="locationId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location*</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(Number(value))}
                          defaultValue={
                            field.value ? String(field.value) : undefined
                          }
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a location" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locations.map((loc) => (
                              <SelectItem key={loc.ID} value={String(loc.ID)}>
                                {loc.Name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-x-4">
                    <FormField
                      control={form.control}
                      name="purchase_unit_price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Purchase Price</FormLabel>
                          <div className="relative">
                            <FormControl>
                              <Input
                                type="number"
                                min={0}
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                value={field.value === undefined ? "" : field.value}
                                className="pl-10"
                              />
                            </FormControl>
                            <DollarSign className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="replacement_unit_value"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Replacement Value*</FormLabel>
                          <div className="relative">
                            <FormControl>
                              <Input
                                type="number"
                                min={0}
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                value={field.value === undefined ? "" : field.value}
                                className="pl-10"
                              />
                            </FormControl>
                            <DollarSign className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem className="h-full flex flex-col">
                      <FormLabel>Notes</FormLabel>
                      <FormControl className="flex-grow">
                        <Textarea
                          placeholder="Additional notes about the item"
                          {...field}
                          className="min-h-[200px] h-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* New Combined Card: Identification & Physical Attributes */}
          <Card className="col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>
                <div className="flex items-center">
                  <Tag className="mr-2 h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-medium">Identification & Physical Attributes</h3>
                </div>
              </CardTitle>
              <CardDescription>Unique identifiers and physical characteristics of the item.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Internal 2-column grid */}
                {/* Column 1: Identification Fields */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="sku"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SKU</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter SKU" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="barcode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Barcode</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              placeholder="Enter barcode"
                              {...field}
                              className="pl-10"
                            />
                          </FormControl>
                          <Barcode className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Column 2: Physical Attribute Fields */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter color" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="dimensions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dimensions</FormLabel>
                        <FormControl>
                          <Input placeholder="L x W x H" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="style"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Style</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter style" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            type="button"
            onClick={() => form.reset(defaultValues)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading || !form.formState.isValid}
            onClick={(e) => {
              // Additional safety measure to ensure the form doesn't submit multiple times
              if (isLoading) {
                e.preventDefault();
                return;
              }
            }}
          >
            {isLoading ? "Saving..." : "Save Item"}
          </Button>
        </div>
      </form>
      {isDevelopment && (
        <Button
          type="button"
          onClick={handleFillForm}
          className="fixed bottom-5 right-5 z-50 p-3 h-auto rounded-full shadow-xl bg-secondary text-secondary-foreground hover:bg-secondary/90"
          title="Fill Form with Mock Data (Dev Only)"
        >
          <TestTube className="h-6 w-6" />
          <span className="sr-only">Fill Form with Mock Data</span>
        </Button>
      )}
    </Form>
  );
};

export default InventoryItemForm;
