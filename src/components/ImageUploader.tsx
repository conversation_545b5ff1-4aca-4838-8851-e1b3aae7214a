import { Image as ImageIcon, Upload, X } from "lucide-react";
import React, { useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ImageUploaderProps {
  images: string[];
  setImages: React.Dispatch<React.SetStateAction<string[]>>;
  maxImages?: number;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  images,
  setImages,
  maxImages = 10,
}) => {
  const [dragging, setDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const processFiles = (files: FileList) => {
    const newFiles = Array.from(files);

    if (images.length + newFiles.length > maxImages) {
      toast.error(`You can only upload a maximum of ${maxImages} images.`);
      return;
    }

    const validFiles = newFiles.filter(file => {
      if (!file.type.startsWith('image/')) {
        toast.error(`File ${file.name} is not an image`);
        return false;
      }
      return true;
    });

    if (validFiles.length === 0) return;

    const toastId = toast.loading(`Processing ${validFiles.length} image(s)...`);
    let processedCount = 0;
    const newImageUrls: string[] = [];

    validFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          newImageUrls.push(event.target.result as string);
          processedCount++;

          if (processedCount === validFiles.length) {
            setImages(prev => [...prev, ...newImageUrls]);
            toast.success(`${newImageUrls.length} image(s) added.`, { id: toastId });
          }
        }
      };
      reader.onerror = () => {
        toast.error(`Failed to process image: ${file.name}`);
        processedCount++;

        if (processedCount === validFiles.length && newImageUrls.length > 0) {
          setImages(prev => [...prev, ...newImageUrls]);
          toast.success(`${newImageUrls.length} image(s) added.`, { id: toastId });
        } else if (processedCount === validFiles.length) {
          toast.error("No images could be processed.", { id: toastId });
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const openFileDialog = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      processFiles(event.target.files);
    }
    // Reset the input value to allow selecting the same files again if needed
    event.target.value = "";
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = () => {
    setDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(false);

    if (e.dataTransfer.files?.length) {
      processFiles(e.dataTransfer.files);
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-3">
      <input
        type="file"
        multiple
        ref={fileInputRef}
        onChange={handleFileChange}
        style={{ display: "none" }}
        accept="image/*"
      />

      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center",
          dragging ? "border-primary bg-primary/5" : "border-border"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center space-y-3">
          <div className="rounded-full bg-primary/10 p-4">
            <Upload className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-medium">Drag & drop your images</h3>
            <p className="text-sm text-muted-foreground mt-1">
              or click to browse your files
            </p>
          </div>
          <Button
            type="button"
            variant="outline"
            onClick={openFileDialog}
          >
            Upload Images
          </Button>
          <p className="text-xs text-muted-foreground">
            Supported formats: JPEG, PNG, GIF, WEBP • Max {maxImages} images
          </p>
        </div>
      </div>

      {images.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mt-4">
          {images.map((src, index) => (
            <div key={index} className="relative group">
              <div className="relative aspect-square overflow-hidden rounded-md border">
                <img
                  src={src}
                  alt={`Uploaded image ${index + 1}`}
                  className="h-full w-full object-cover"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute top-1 right-1 bg-black/70 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}

          {images.length < maxImages && (
            <div
              className="relative aspect-square flex items-center justify-center h-full w-full border-2 border-dashed rounded-md cursor-pointer hover:bg-muted/50"
              onClick={openFileDialog}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  openFileDialog(e);
                }
              }}
            >
              <div className="flex flex-col items-center">
                <ImageIcon className="h-8 w-8 text-muted-foreground" />
                <span className="text-sm text-muted-foreground mt-2">Add more</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};