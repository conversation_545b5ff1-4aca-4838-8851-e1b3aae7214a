import { Image as ImageIcon, Upload, X } from "lucide-react";
import React, { useState } from "react";

import { Button } from "@/components/ui/button";
import { ImageUploadService } from "../../bindings/stagewise";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ImageUploaderProps {
  images: string[];
  setImages: React.Dispatch<React.SetStateAction<string[]>>;
  maxImages?: number;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  images,
  setImages,
  maxImages = 10,
}) => {
  const [dragging, setDragging] = useState(false);

  const openImageDialogAndProcess = async (e: React.MouseEvent | React.KeyboardEvent) => {
    // Prevent the event from bubbling up to the form
    e.preventDefault();
    e.stopPropagation();

    try {
      const filePaths = await ImageUploadService.OpenMultipleImagesDialog();
      if (!filePaths || filePaths.length === 0) {
        // User cancelled or no files selected
        return;
      }

      if (images.length + filePaths.length > maxImages) {
        toast.error(`You can only upload a maximum of ${maxImages} images.`);
        return;
      }

      const newImageUrls: string[] = [];
      // Simple progress tracking, can be enhanced
      let processedCount = 0;
      const toastId = toast.loading(`Processing ${filePaths.length} image(s)...`);

      for (const filePath of filePaths) {
        try {
          // Assuming ProcessImage returns [base64Data, filename]
          const [base64Data, _filename] = await ImageUploadService.ProcessImage(filePath, 1200, 1200);
          if (base64Data) {
            newImageUrls.push(base64Data);
          }
          processedCount++;
          toast.loading(`Processed ${processedCount} of ${filePaths.length} image(s)...`, { id: toastId });
        } catch (error) {
          console.error(`Error processing image ${filePath}:`, error);
          toast.error(`Failed to process image: ${filePath.split('/').pop() || filePath}`);
        }
      }

      if (newImageUrls.length > 0) {
        setImages(prevImages => [...prevImages, ...newImageUrls]);
        toast.success(`${newImageUrls.length} image(s) added.`, { id: toastId });
      } else if (filePaths.length > 0) {
         toast.error("No images could be processed.", { id: toastId });
      } else {
        toast.dismiss(toastId); // Dismiss if no files were selected initially
      }

    } catch (error) {
      console.error("Error opening image dialog or processing images:", error);
      toast.error(error instanceof Error ? error.message : "An error occurred while adding images.");
    }
  };