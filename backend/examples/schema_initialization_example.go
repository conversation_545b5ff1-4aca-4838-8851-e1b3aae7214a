package main

import (
	"log"
	"stagewise-backend/internal"

	"github.com/pocketbase/pocketbase"
)

// Example demonstrating how to use the schema initialization functionality
func main() {
	// Initialize PocketBase
	app := pocketbase.New()

	// Example 1: Initialize schema (recommended approach)
	// This is the main function you should use during application startup
	log.Println("=== Example 1: Initialize Schema ===")
	if err := internal.InitializeSchema(app); err != nil {
		log.Printf("Failed to initialize schema: %v", err)
	}

	// Example 2: Just validate what exists without making changes
	log.Println("\n=== Example 2: Validate Schema ===")
	allExist, existing, missing, err := internal.ValidateGeneratedSchema(app)
	if err != nil {
		log.Printf("Failed to validate schema: %v", err)
	} else {
		log.Printf("All collections exist: %v", allExist)
		log.Printf("Existing collections: %v", existing)
		log.Printf("Missing collections: %v", missing)
	}

	// Example 3: Ensure collections exist (more detailed control)
	log.Println("\n=== Example 3: Ensure Collections Exist ===")
	if err := internal.EnsureGeneratedCollectionsExist(app); err != nil {
		log.Printf("Failed to ensure collections exist: %v", err)
	}

	// Example 4: Create only missing collections (strict mode)
	log.Println("\n=== Example 4: Create Missing Collections Only ===")
	if err := internal.CreateMissingCollectionsOnly(app); err != nil {
		log.Printf("Failed to create missing collections: %v", err)
	}

	log.Println("\n=== All examples completed ===")
}

/*
Usage scenarios:

1. **Application Startup (Recommended)**:
   Use InitializeSchema() in your main application startup code.
   This provides the best user experience with clear logging.

2. **Health Checks**:
   Use ValidateGeneratedSchema() to check if your schema is complete
   without making any changes.

3. **Migration Scripts**:
   Use EnsureGeneratedCollectionsExist() when you want detailed control
   over the creation process with partial failure handling.

4. **Strict Deployment**:
   Use CreateMissingCollectionsOnly() when you want to fail fast
   if any collection creation fails.

Integration examples:

// In your main server startup:
app.OnServe().BindFunc(func(se *core.ServeEvent) error {
    if err := internal.InitializeSchema(se.App); err != nil {
        log.Printf("Warning: Schema initialization failed: %v", err)
        // Decide whether to continue or fail based on your requirements
    }
    return se.Next()
})

// In a health check endpoint:
func healthCheck(app core.App) (bool, error) {
    allExist, _, missing, err := internal.ValidateGeneratedSchema(app)
    if err != nil {
        return false, err
    }
    if !allExist {
        return false, fmt.Errorf("missing collections: %v", missing)
    }
    return true, nil
}

// In a migration script:
func runMigrations(app core.App) error {
    // First ensure schema is complete
    if err := internal.EnsureGeneratedCollectionsExist(app); err != nil {
        return fmt.Errorf("schema initialization failed: %w", err)
    }
    
    // Then run your data migrations
    return runDataMigrations(app)
}
*/
