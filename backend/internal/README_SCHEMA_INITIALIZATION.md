# Schema Initialization

This module provides functionality to automatically ensure that all collections from the generated schema exist in the PocketBase database. If any collections are missing, they will be created automatically using the generated creation functions.

## Overview

The schema initialization system works by:

1. **Reading the generated schema**: Uses the metadata from `generated_pocketbase_models.go`
2. **Checking existing collections**: Queries the database to see which collections already exist
3. **Creating missing collections**: Runs the appropriate creation functions for any missing collections
4. **Handling dependencies**: Creates collections in the correct order to handle relations

## Functions

### `InitializeSchema(app core.App) error`

**Recommended function for most use cases.**

This is the main function you should call during application startup. It:
- Validates the current schema state
- Logs detailed information about what exists and what's missing
- Creates any missing collections
- Provides clear success/failure feedback

```go
if err := internal.InitializeSchema(app); err != nil {
    log.Fatalf("Failed to initialize schema: %v", err)
}
```

### `ValidateGeneratedSchema(app core.App) (bool, []string, []string, error)`

Checks the schema without making any changes. Returns:
- `bool`: Whether all collections exist
- `[]string`: List of existing collection names
- `[]string`: List of missing collection names
- `error`: Any error that occurred during validation

```go
allExist, existing, missing, err := internal.ValidateGeneratedSchema(app)
if err != nil {
    return err
}
if !allExist {
    log.Printf("Missing collections: %v", missing)
}
```

### `EnsureGeneratedCollectionsExist(app core.App) error`

Creates missing collections with detailed logging and partial failure handling. If some collections fail to create, it will continue trying to create others and return information about what succeeded/failed.

```go
if err := internal.EnsureGeneratedCollectionsExist(app); err != nil {
    log.Printf("Some collections failed to create: %v", err)
}
```

### `CreateMissingCollectionsOnly(app core.App) error`

Creates only the missing collections. Fails fast - if any collection creation fails, the function returns immediately with an error.

```go
if err := internal.CreateMissingCollectionsOnly(app); err != nil {
    return fmt.Errorf("failed to create missing collections: %w", err)
}
```

## Integration Examples

### Server Startup

Add to your main server startup code:

```go
app.OnServe().BindFunc(func(se *core.ServeEvent) error {
    log.Println("Server starting...")
    
    // Ensure schema is complete
    if err := internal.InitializeSchema(se.App); err != nil {
        log.Printf("Warning: Schema initialization failed: %v", err)
        // Decide whether to continue or fail based on your requirements
    }
    
    return se.Next()
})
```

### Health Check Endpoint

```go
func healthCheck(app core.App) (bool, error) {
    allExist, _, missing, err := internal.ValidateGeneratedSchema(app)
    if err != nil {
        return false, err
    }
    if !allExist {
        return false, fmt.Errorf("missing collections: %v", missing)
    }
    return true, nil
}
```

### Migration Script

```go
func runMigrations(app core.App) error {
    // First ensure schema is complete
    if err := internal.EnsureGeneratedCollectionsExist(app); err != nil {
        return fmt.Errorf("schema initialization failed: %w", err)
    }
    
    // Then run your data migrations
    return runDataMigrations(app)
}
```

## Dependency Handling

The system handles collection dependencies automatically by:

1. **Using the correct creation order**: Collections are created in the same order as defined in `GetAllGeneratedCollections()`
2. **Relation validation**: Each creation function validates that its dependencies exist before creating relations
3. **Graceful failure handling**: If a dependency is missing, the creation function will return a descriptive error

## Logging

The system provides detailed logging at each step:

```
Initializing database schema from generated models...
Checking for missing collections from generated schema...
Collection 'clients' already exists
Collection 'item_types' already exists
Collection 'locations' is missing
Found 2 existing collections: [clients item_types]
Found 1 missing collections: [locations]
Creating 1 missing collections...
Creating collection 'locations'...
Successfully created collection 'locations'
✅ Schema initialization complete
```

## Error Handling

The functions provide different levels of error handling:

- **`InitializeSchema`**: Logs warnings but doesn't fail server startup
- **`ValidateGeneratedSchema`**: Returns detailed information about what's missing
- **`EnsureGeneratedCollectionsExist`**: Continues on partial failures, returns summary
- **`CreateMissingCollectionsOnly`**: Fails fast on any error

Choose the appropriate function based on your error handling requirements.
