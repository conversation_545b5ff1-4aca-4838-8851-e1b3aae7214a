package internal

import (
	"fmt"
	"log"

	"github.com/pocketbase/pocketbase/core"
)

// CollectionInfo holds metadata about a generated collection and its creation function
type CollectionInfo struct {
	Name       string
	CreateFunc func(core.App) error
}

// GetAllGeneratedCollections returns metadata for all generated collections
// in the correct dependency order for creation
func GetAllGeneratedCollections() []CollectionInfo {
	return []CollectionInfo{
		{
			Name:       "clients",
			CreateFunc: CreateClientsCollection,
		},
		{
			Name:       "item_types",
			CreateFunc: CreateItemTypesCollection,
		},
		{
			Name:       "locations",
			CreateFunc: CreateLocationsCollection,
		},
		{
			Name:       "inventory_locations",
			CreateFunc: CreateInventoryLocationsCollection,
		},
		{
			Name:       "item_instances",
			CreateFunc: CreateItemInstancesCollection,
		},
		{
			Name:       "projects",
			CreateFunc: CreateProjectsCollection,
		},
		{
			Name:       "project_inventories",
			CreateFunc: CreateProjectInventoriesCollection,
		},
		{
			Name:       "view_active_projects",
			CreateFunc: CreateViewActiveProjectsCollection,
		},
	}
}

// EnsureGeneratedCollectionsExist checks if all collections from the generated schema exist,
// and creates any missing collections by running their creation functions.
// This function is designed to be called during application startup to ensure
// the database schema is properly initialized.
func EnsureGeneratedCollectionsExist(app core.App) error {
	log.Println("Checking for missing collections from generated schema...")

	// Get all generated collections metadata
	collections := GetAllGeneratedCollections()

	var missingCollections []CollectionInfo
	var existingCollections []string

	// Check which collections are missing
	for _, collectionInfo := range collections {
		_, err := app.FindCollectionByNameOrId(collectionInfo.Name)
		if err != nil {
			// Collection doesn't exist, add to missing list
			missingCollections = append(missingCollections, collectionInfo)
			log.Printf("Collection '%s' is missing", collectionInfo.Name)
		} else {
			// Collection exists
			existingCollections = append(existingCollections, collectionInfo.Name)
			log.Printf("Collection '%s' already exists", collectionInfo.Name)
		}
	}

	// Log summary
	log.Printf("Found %d existing collections: %v", len(existingCollections), existingCollections)
	log.Printf("Found %d missing collections: %v", len(missingCollections), getMissingCollectionNames(missingCollections))

	// If no collections are missing, we're done
	if len(missingCollections) == 0 {
		log.Println("All collections from generated schema already exist. No action needed.")
		return nil
	}

	// Create missing collections
	log.Printf("Creating %d missing collections...", len(missingCollections))

	// We need to create collections in dependency order to handle relations properly
	// The order in GetAllGeneratedCollections() is already arranged to handle dependencies
	createdCount := 0
	var lastError error

	for _, collectionInfo := range missingCollections {
		log.Printf("Creating collection '%s'...", collectionInfo.Name)

		err := collectionInfo.CreateFunc(app)
		if err != nil {
			log.Printf("Error creating collection '%s': %v", collectionInfo.Name, err)
			lastError = err
			// Continue trying to create other collections rather than failing immediately
			// This allows for partial recovery in case some collections can be created
			continue
		}

		log.Printf("Successfully created collection '%s'", collectionInfo.Name)
		createdCount++
	}

	// Report results
	if createdCount == len(missingCollections) {
		log.Printf("Successfully created all %d missing collections", createdCount)
		return nil
	} else if createdCount > 0 {
		log.Printf("Created %d out of %d missing collections. Some collections failed to create.", createdCount, len(missingCollections))
		return fmt.Errorf("partially successful: created %d/%d collections, last error: %w", createdCount, len(missingCollections), lastError)
	} else {
		log.Printf("Failed to create any of the %d missing collections", len(missingCollections))
		return fmt.Errorf("failed to create any missing collections, last error: %w", lastError)
	}
}

// CreateMissingCollectionsOnly creates only the collections that are missing from the database.
// This is a more targeted version of EnsureGeneratedCollectionsExist that only creates
// what's actually missing, without checking existing collections.
func CreateMissingCollectionsOnly(app core.App) error {
	log.Println("Creating missing collections from generated schema...")

	// Get all generated collections metadata
	collections := GetAllGeneratedCollections()

	var missingCollections []CollectionInfo

	// Check which collections are missing
	for _, collectionInfo := range collections {
		_, err := app.FindCollectionByNameOrId(collectionInfo.Name)
		if err != nil {
			// Collection doesn't exist, add to missing list
			missingCollections = append(missingCollections, collectionInfo)
		}
	}

	// If no collections are missing, we're done
	if len(missingCollections) == 0 {
		log.Println("No missing collections found.")
		return nil
	}

	log.Printf("Creating %d missing collections: %v", len(missingCollections), getMissingCollectionNames(missingCollections))

	// Create missing collections in dependency order
	for _, collectionInfo := range missingCollections {
		log.Printf("Creating collection '%s'...", collectionInfo.Name)

		err := collectionInfo.CreateFunc(app)
		if err != nil {
			return fmt.Errorf("failed to create collection '%s': %w", collectionInfo.Name, err)
		}

		log.Printf("Successfully created collection '%s'", collectionInfo.Name)
	}

	log.Printf("Successfully created all %d missing collections", len(missingCollections))
	return nil
}

// ValidateGeneratedSchema checks if all collections from the generated schema exist
// and returns a report without making any changes.
func ValidateGeneratedSchema(app core.App) (bool, []string, []string, error) {
	collections := GetAllGeneratedCollections()

	var existingCollections []string
	var missingCollections []string

	for _, collectionInfo := range collections {
		_, err := app.FindCollectionByNameOrId(collectionInfo.Name)
		if err != nil {
			missingCollections = append(missingCollections, collectionInfo.Name)
		} else {
			existingCollections = append(existingCollections, collectionInfo.Name)
		}
	}

	allExist := len(missingCollections) == 0
	return allExist, existingCollections, missingCollections, nil
}

// Helper function to extract collection names from CollectionInfo slice
func getMissingCollectionNames(collections []CollectionInfo) []string {
	names := make([]string, len(collections))
	for i, collection := range collections {
		names[i] = collection.Name
	}
	return names
}

// InitializeSchema is a convenience function that ensures all generated collections exist.
// This is the main function you should call during application startup.
// It will:
// 1. Check which collections from the generated schema are missing
// 2. Create any missing collections in the proper dependency order
// 3. Log the process and results
//
// Usage example:
//
//	if err := internal.InitializeSchema(app); err != nil {
//	    log.Fatalf("Failed to initialize schema: %v", err)
//	}
func InitializeSchema(app core.App) error {
	log.Println("Initializing database schema from generated models...")

	// First, validate what exists
	allExist, existing, missing, err := ValidateGeneratedSchema(app)
	if err != nil {
		return fmt.Errorf("failed to validate schema: %w", err)
	}

	log.Printf("Schema validation complete. Existing: %d, Missing: %d", len(existing), len(missing))

	if allExist {
		log.Println("✅ All collections from generated schema are present")
		return nil
	}

	log.Printf("⚠️  Missing collections detected: %v", missing)
	log.Println("🔧 Creating missing collections...")

	// Create missing collections
	err = EnsureGeneratedCollectionsExist(app)
	if err != nil {
		return fmt.Errorf("failed to create missing collections: %w", err)
	}

	log.Println("✅ Schema initialization complete")
	return nil
}
