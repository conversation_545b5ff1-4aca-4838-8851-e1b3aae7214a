package internal

import (
	"fmt"
	"log"
	"strconv"

	"github.com/pocketbase/pocketbase/core"
)

func RegisterHooks(app core.App) error {
	if err := registerItemTypeCreationHook(app); err != nil {
		return fmt.Errorf("failed to register item type creation hook: %w", err)
	}
	if err := registerAdminSecurityHeaders(app); err != nil {
		return fmt.Errorf("failed to register admin security headers hook: %w", err)
	}
	return nil
}

// registerItemTypeCreationHook sets up a hook to automatically create item instances
// when a new item type is created based on the quantity field
func registerItemTypeCreationHook(app core.App) error {
	// Register hook for item_type create requests
	app.OnRecordCreateRequest("item_types").BindFunc(func(e *core.RecordRequestEvent) error {
		itemType := e.Record
		if itemType == nil {
			return fmt.Errorf("received nil item_type record")
		}

		log.Printf("Processing item_type creation request: %v", itemType.FieldsData())

		// Get the quantity field value - try both number and string methods
		quantity := 0

		// Try to get as number first
		if val := itemType.Get("total_quantity"); val != nil {
			if num, ok := val.(float64); ok {
				quantity = int(num)
			} else if numStr := itemType.GetString("total_quantity"); numStr != "" {
				// Try to parse as string if not a number
				if parsedNum, parseErr := strconv.Atoi(numStr); parseErr == nil {
					quantity = parsedNum
				} else {
					log.Printf("Failed to parse quantity '%s' as integer: %v", numStr, parseErr)
				}
			}
		}

		// Set default value if we couldn't get a valid quantity
		if quantity <= 0 {
			log.Printf("Invalid or missing quantity value for item_type, defaulting to 1")
			quantity = 1
		}

		// First let the normal item_type creation proceed
		if err := e.Next(); err != nil {
			return fmt.Errorf("failed to create item_type: %w", err)
		}

		// Now use a transaction just for creating the instances
		return app.RunInTransaction(func(txApp core.App) error {
			// Get the collection for item_instances
			instancesCollection, err := txApp.FindCollectionByNameOrId("item_instances")
			if err != nil {
				return fmt.Errorf("failed to find item_instances collection: %w", err)
			}

			log.Printf("Creating %d item_instances for item_type %s", quantity, itemType.Id)

			successCount := 0
			// Create the specified number of item_instances
			for i := 0; i < quantity; i++ {
				// Create a new item_instance record
				instance := core.NewRecord(instancesCollection)
				if instance == nil {
					log.Printf("Failed to create new record for instance %d: got nil", i+1)
					continue
				}

				// Set the required fields
				instance.Set("item_type", itemType.Id)

				// Copy relevant fields from the item_type to the instance
				instance.Set("name", itemType.GetString("name")+" #"+strconv.Itoa(i+1))
				instance.Set("description", itemType.GetString("description"))

				// Set status to "Available" by default for new instances
				instance.Set("status", "Available")

				// Set in_service to true by default
				instance.Set("in_service", true)

				// Save the new instance - use txApp, not app
				if err := txApp.Save(instance); err != nil {
					log.Printf("Failed to create item_instance %d for item_type %s: %v", i+1, itemType.Id, err)
					continue // Continue trying to create other instances
				}

				successCount++
			}

			if successCount == 0 && quantity > 0 {
				return fmt.Errorf("failed to create any item_instances for item_type %s", itemType.Id)
			}

			if successCount < quantity {
				log.Printf("Warning: Only created %d/%d item_instances for item_type %s",
					successCount, quantity, itemType.Id)
			} else {
				log.Printf("Successfully created %d item_instances for item_type %s",
					successCount, itemType.Id)
			}

			return nil
		})
	})

	return nil
}

// registerAdminSecurityHeaders modifies security headers for the admin interface
// to allow iframe embedding from localhost/127.0.0.1 during development
func registerAdminSecurityHeaders(app core.App) error {
	// Hook into the serve event to add custom routes and middleware
	app.OnServe().BindFunc(func(se *core.ServeEvent) error {
		// Add a custom route handler for the admin interface
		se.Router.GET("/_/*", func(e *core.RequestEvent) error {
			// Get the origin and referer headers
			origin := e.Request.Header.Get("Origin")
			referer := e.Request.Header.Get("Referer")

			// Allow iframe embedding from localhost and 127.0.0.1 during development
			if origin == "http://localhost:5173" ||
				origin == "http://127.0.0.1:5173" ||
				referer == "http://localhost:5173/" ||
				referer == "http://127.0.0.1:5173/" {
				// Remove the X-Frame-Options header to allow iframe embedding
				e.Response.Header().Del("X-Frame-Options")
				log.Printf("Allowing iframe embedding for admin interface from origin: %s, referer: %s", origin, referer)
			}

			// Continue with the default admin handler
			return e.Next()
		} /* middlewares */)

		return se.Next()
	})

	return nil
}
