package main

import (
	"log"
	"os"
	"path/filepath"
	"stagewise-backend/internal"

	"github.com/pocketbase/pocketbase"
	"github.com/pocketbase/pocketbase/core"
	"github.com/pocketbase/pocketbase/plugins/migratecmd"
)

func main() {
	// Configure data directory based on environment
	dataDir := getDataDirectory()

	// Ensure data directory exists
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		log.Fatalf("Failed to create data directory %s: %v", dataDir, err)
	}

	log.Printf("Using data directory: %s", dataDir)

	// Initialize PocketBase with custom data directory
	app := pocketbase.NewWithConfig(pocketbase.Config{
		DefaultDataDir: dataDir,
	})

	// Setup migrations
	migratecmd.MustRegister(app, app.RootCmd, migratecmd.Config{
		Automigrate: true,
	})

	// Initialize schema - ensure all generated collections exist
	app.OnServe().BindFunc(func(se *core.ServeEvent) error {
		log.Println("StageWise PocketBase server starting...")

		// Ensure all collections from generated schema exist
		if err := internal.InitializeSchema(se.App); err != nil {
			log.Printf("Warning: Failed to initialize schema: %v", err)
			// Don't fail the server start, just log the warning
		}

		return se.Next()
	})

	if err := app.Start(); err != nil {
		log.Fatal(err)
	}
}

// getDataDirectory returns the appropriate data directory based on environment
func getDataDirectory() string {
	// Check for explicit environment variable first
	if dataDir := os.Getenv("POCKETBASE_DATA_DIR"); dataDir != "" {
		return dataDir
	}

	// Determine based on environment
	env := os.Getenv("ENVIRONMENT")
	switch env {
	case "production":
		// Production: Use /var/lib/stagewise for persistence
		return "/var/lib/stagewise"
	case "development":
		// Development: Use ./pb_data relative to backend directory
		return "./pb_data"
	case "docker":
		// Docker: Use mounted volume
		return "/app/data"
	default:
		// Default: Use ./pb_data relative to current directory
		if wd, err := os.Getwd(); err == nil {
			// If we're in cmd/server, go up two levels to backend root
			if filepath.Base(wd) == "server" {
				return filepath.Join(filepath.Dir(filepath.Dir(wd)), "pb_data")
			}
		}
		return "./pb_data"
	}
}
