#!/bin/bash

# StageWise Cloud Development Setup Script

set -e

echo "🚀 Setting up StageWise Cloud development environment..."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ first."
    exit 1
fi

# Check if Bun is installed
if ! command -v bun &> /dev/null; then
    echo "❌ Bun is not installed. Please install Bun first."
    echo "📖 Install from: https://bun.sh"
    exit 1
fi

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
bun install

# Setup backend
echo "🔧 Setting up backend..."
cd backend
go mod tidy

# Generate Go models from schema
echo "🏗️  Generating Go models from schema..."
go run cmd/schema_gen/main.go \
  -inputFile schema/collections.json \
  -outputFile internal/generated_pocketbase_models.go \
  -packageName internal

cd ..

# Create .env if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️  Creating .env file..."
    cp .env.example .env
fi

echo "✅ Development environment setup complete!"
echo ""
echo "🎯 Quick start commands:"
echo "  bun run dev:local            # Start full stack locally"
echo "  bun run fullstack:dev        # Start full stack with Docker"
echo "  bun run backend:dev          # Start backend only"
echo "  bun run dev                  # Start frontend only"
echo "  bun run backend:create-admin # Create admin user"
echo "  bun run backend:seed         # Seed database with test data"
echo "  bun run types:generate       # Generate TypeScript types"
echo ""
echo "📝 Note: Environment variables are in .env.local"
echo "  Copy and customize .env.local for your setup"